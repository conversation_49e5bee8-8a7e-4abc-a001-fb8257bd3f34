"use client";

import { useState, useRef, useEffect } from "react";
import Image from "next/image";
import { Send, X, Paperclip, Wand2, AtSign, Brain, BookOpen, MessageCircle, GitBranch, Menu, Plus, Search } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Toggle } from "@/components/ui/toggle";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  MobileDialog,
  MobileDialogContent,
  MobileDialogHeader,
  MobileDialogTitle,
} from "@/components/ui/mobile-dialog";
import { VisuallyHidden } from "@radix-ui/react-visually-hidden";
import { cn } from "@/lib/utils";

// Custom N Icon component
const NIcon = ({ className }: { className?: string }) => (
  <Image
    src="/NeoTask_Icon_N.webp"
    alt="Agent"
    width={20}
    height={20}
    className={`${className} object-contain`}
  />
);

interface Message {
  id: string;
  text: string;
  isUser: boolean;
  timestamp: Date;
}

interface SelectedTag {
  id: string;
  type: 'list' | 'task';
  name: string;
}

interface MobileChatModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function MobileChatModal({ open, onOpenChange }: MobileChatModalProps) {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      text: 'Hello! How can I help you with your tasks today?',
      isUser: false,
      timestamp: new Date(),
    },
  ]);
  const [inputValue, setInputValue] = useState("");
  const [isTyping, setIsTyping] = useState(false);
  const [selectedTags, setSelectedTags] = useState<SelectedTag[]>([]);
  const [isAutoMode, setIsAutoMode] = useState(true);
  const [isQuestionMode, setIsQuestionMode] = useState(false);
  const [selectedModel, setSelectedModel] = useState("gpt-4");
  const [isConversationsOpen, setIsConversationsOpen] = useState(false);
  const [isListsMenuOpen, setIsListsMenuOpen] = useState(false);
  const [isMemoriesOpen, setIsMemoriesOpen] = useState(false);
  const [isRulesOpen, setIsRulesOpen] = useState(false);
  const [keyboardHeight, setKeyboardHeight] = useState(0);

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLTextAreaElement>(null);

  // Mock data
  const mockConversations = [
    { id: '1', title: 'Task Management Help', lastMessage: 'How do I organize my tasks?' },
    { id: '2', title: 'Project Planning', lastMessage: 'Can you help me plan my project?' },
    { id: '3', title: 'Productivity Tips', lastMessage: 'What are some productivity tips?' },
  ];

  const mockLists = [
    { id: '1', name: 'Personal Tasks' },
    { id: '2', name: 'Work Projects' },
    { id: '3', name: 'Shopping List' },
  ];

  // Auto-scroll to bottom when new messages are added
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);



  // Keyboard detection for mobile
  useEffect(() => {
    if (typeof window === 'undefined') return;

    const handleViewportChange = () => {
      // Only handle this on mobile devices
      if (window.innerWidth > 767) return;

      const viewport = window.visualViewport;
      if (viewport) {
        const keyboardHeight = window.innerHeight - viewport.height;
        // Only set keyboard height if it's significant (> 100px to avoid false positives)
        setKeyboardHeight(keyboardHeight > 100 ? keyboardHeight : 0);
      }
    };

    // Listen for viewport changes
    if (window.visualViewport) {
      window.visualViewport.addEventListener('resize', handleViewportChange);
      // Initial check
      handleViewportChange();
    }

    return () => {
      if (window.visualViewport) {
        window.visualViewport.removeEventListener('resize', handleViewportChange);
      }
    };
  }, []);

  // Auto-resize textarea
  useEffect(() => {
    const textarea = inputRef.current;
    if (textarea) {
      textarea.style.height = 'auto';
      const newHeight = Math.min(Math.max(textarea.scrollHeight, 40), 120);
      textarea.style.height = `${newHeight}px`;
    }
  }, [inputValue]);

  // Scroll to bottom when messages change (but not on mobile for user messages)
  useEffect(() => {
    const scrollToBottom = () => {
      if (messagesEndRef.current) {
        // On mobile, don't auto-scroll to bottom for user messages (we handle that separately)
        const isMobile = typeof window !== 'undefined' && window.innerWidth <= 767;
        const lastMessage = messages[messages.length - 1];

        if (isMobile && lastMessage?.isUser) {
          // Skip auto-scroll to bottom for user messages on mobile
          return;
        }

        // Scroll to bottom within the messages container only
        if (messagesContainerRef.current) {
          messagesContainerRef.current.scrollTo({
            top: messagesContainerRef.current.scrollHeight,
            behavior: 'smooth'
          });
        }
      }
    };

    // Small delay to ensure layout has updated
    const timeoutId = setTimeout(scrollToBottom, 100);
    return () => clearTimeout(timeoutId);
  }, [messages]);

  const handleSendMessage = () => {
    if (!inputValue.trim()) return;

    const newMessage: Message = {
      id: Date.now().toString(),
      text: inputValue,
      isUser: true,
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, newMessage]);
    setInputValue("");
    setIsTyping(true);

    // On mobile, scroll the new message to the top of the visible area
    if (typeof window !== 'undefined' && window.innerWidth <= 767 && messagesContainerRef.current) {
      setTimeout(() => {
        const container = messagesContainerRef.current;
        if (container) {
          // Find the last message element (the one we just added)
          const messageElements = container.querySelectorAll('[data-message-id]');
          const lastMessage = messageElements[messageElements.length - 1] as HTMLElement;

          if (lastMessage) {
            // Scroll within the messages container so the new message appears at the top
            const messageOffsetTop = lastMessage.offsetTop;
            container.scrollTo({
              top: messageOffsetTop,
              behavior: 'smooth'
            });
          }
        }
      }, 150);
    }

    // Simulate AI response
    setTimeout(() => {
      const aiResponse: Message = {
        id: (Date.now() + 1).toString(),
        text: "I understand you're asking about: " + inputValue + ". Let me help you with that!",
        isUser: false,
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, aiResponse]);
      setIsTyping(false);
    }, 1500);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    // Allow Enter to create new lines - no special handling needed
    // Users will use the Send button to submit messages
  };

  const addTag = (tag: SelectedTag) => {
    if (!selectedTags.find(t => t.id === tag.id && t.type === tag.type)) {
      setSelectedTags(prev => [...prev, tag]);
    }
  };

  const removeTag = (tagId: string, tagType: string) => {
    setSelectedTags(prev => prev.filter(tag => !(tag.id === tagId && tag.type === tagType)));
  };



  return (
    <MobileDialog open={open} onOpenChange={onOpenChange}>
      <MobileDialogContent fullHeight enableSwipeToDismiss>
        <VisuallyHidden asChild>
          <MobileDialogTitle>Chat</MobileDialogTitle>
        </VisuallyHidden>
        
        <div
          className="flex flex-col px-4 pt-4 pb-4 overflow-hidden"
          style={{
            height: keyboardHeight > 0 ? `calc(100dvh - ${keyboardHeight + 32}px)` : '100dvh',
            maxHeight: keyboardHeight > 0 ? `calc(100dvh - ${keyboardHeight + 32}px)` : '100dvh',
          }}
        >
          {/* Header */}
          <div className="flex items-center justify-between pb-4">
            <div className="flex items-center gap-2">
              <NIcon className="h-5 w-5 flex-shrink-0" />
              <h2 className="font-medium">Agent</h2>
            </div>
            {/* Right: Hamburger Menu and New Chat Button */}
            <div className="flex items-center gap-2">
              {/* Hamburger Menu */}
              <Popover open={isConversationsOpen} onOpenChange={setIsConversationsOpen}>
                <PopoverTrigger asChild>
                  <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                    <Menu className="h-4 w-4" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-80 p-0" align="end">
                  <div className="p-3">
                    <div className="flex items-center justify-between mb-3">
                      <h3 className="font-medium text-sm">Conversations</h3>
                      <div className="flex items-center gap-1">
                        <Button variant="ghost" size="sm" className="h-7 w-7 p-0">
                          <Plus className="h-3 w-3" />
                        </Button>
                        <Button variant="ghost" size="sm" className="h-7 w-7 p-0">
                          <Search className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                    <div className="space-y-1 max-h-64 overflow-y-auto">
                      {mockConversations.map((conversation) => (
                        <button
                          key={conversation.id}
                          type="button"
                          className="w-full text-left px-2 py-2 hover:bg-accent/20 rounded text-sm"
                        >
                          <div className="font-medium truncate">{conversation.title}</div>
                          <div className="text-xs text-muted-foreground">{conversation.lastMessage}</div>
                        </button>
                      ))}
                    </div>
                  </div>
                </PopoverContent>
              </Popover>
              {/* New Chat Button */}
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <Plus className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Messages */}
          <div ref={messagesContainerRef} className="flex-1 overflow-y-auto space-y-4 mb-4 min-h-0">
            {messages.map((message) => (
              <div
                key={message.id}
                data-message-id={message.id}
                className={cn(
                  "flex flex-col gap-1",
                  message.isUser ? "items-end" : "items-start"
                )}
              >
                <div
                  className={cn(
                    "text-sm whitespace-pre-wrap break-words",
                    message.isUser
                      ? "max-w-[80%] rounded-lg px-3 py-2 bg-muted text-foreground"
                      : "text-foreground"
                  )}
                >
                  {message.text}
                </div>
              </div>
            ))}
            
            {isTyping && (
              <div className="flex flex-col gap-1 items-start">
                <div className="text-sm">
                  <div className="flex gap-1">
                    <div className="w-2 h-2 bg-current rounded-full animate-bounce" style={{ animationDelay: '0ms' }} />
                    <div className="w-2 h-2 bg-current rounded-full animate-bounce" style={{ animationDelay: '150ms' }} />
                    <div className="w-2 h-2 bg-current rounded-full animate-bounce" style={{ animationDelay: '300ms' }} />
                  </div>
                </div>
              </div>
            )}
            
            <div ref={messagesEndRef} />
          </div>

          {/* Chat Input and Controls Container */}
          <div className="flex-shrink-0">
            {/* Borderless container for mobile, bordered container for desktop */}
            <div className="md:border md:border-border md:rounded-lg p-1 md:p-2 space-y-1">
              {/* Input Field with Send Button */}
              <div className="relative">
                <Textarea
                  ref={inputRef}
                  value={inputValue}
                  onChange={(e) => setInputValue(e.target.value)}
                  onKeyDown={handleKeyDown}
                  placeholder="Ask or instruct NeoTask Agent..."
                  className="w-full min-h-[40px] max-h-[120px] resize-none border-none focus:ring-0 focus:outline-none focus:border-none focus-visible:ring-0 focus-visible:outline-none focus-visible:border-none shadow-none bg-transparent p-0 pr-14"
                  rows={1}
                  style={{ outline: 'none', border: 'none', boxShadow: 'none' }}
                />
                {/* Send Button positioned in bottom right of textarea */}
                <Button
                  onClick={handleSendMessage}
                  disabled={!inputValue.trim()}
                  size="sm"
                  className="absolute bottom-1 right-1 h-10 w-10 p-0 rounded-md"
                >
                  <Send className="h-5 w-5" />
                </Button>
              </div>

              {/* First Row: Auto, Question, Model, and Action Buttons */}
              <div className="flex items-center justify-between gap-1">
                {/* Left side: Auto, Question, Model */}
                <div className="flex items-center gap-1">
                  {/* Auto Toggle */}
                  <Toggle
                    pressed={isAutoMode}
                    onPressedChange={setIsAutoMode}
                    size="sm"
                    className="h-7 px-2 text-xs"
                  >
                    Auto
                  </Toggle>

                  {/* Ask Question Toggle with Message Circle Icon */}
                  <Toggle
                    pressed={isQuestionMode}
                    onPressedChange={setIsQuestionMode}
                    size="sm"
                    className="h-7 w-7 p-0"
                  >
                    <MessageCircle className="h-4 w-4" />
                  </Toggle>

                  {/* Vertical Divider */}
                  <div className="h-4 w-px bg-border mx-1" />

                  {/* AI Model Selection with Switch Icon - styled like tags button */}
                  <Select value={selectedModel} onValueChange={setSelectedModel}>
                    <SelectTrigger className="flex items-center gap-2 text-muted-foreground hover:!text-foreground hover:!bg-muted/50 transition-colors rounded-lg h-7 px-2 border-none bg-transparent focus:ring-0 shadow-none w-auto cursor-pointer">
                      <GitBranch className="h-4 w-4" />
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="gpt-4">GPT-4</SelectItem>
                      <SelectItem value="gpt-3.5">GPT-3.5</SelectItem>
                      <SelectItem value="claude">Claude</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Right side: Action Buttons */}
                <div className="flex items-center gap-1">
                  {/* Paperclip - File Attachment */}
                  <Button variant="ghost" size="sm" className="h-7 w-7 p-0">
                    <Paperclip className="h-3 w-3" />
                  </Button>
                  {/* Magic - Enhance Prompt */}
                  <Button variant="ghost" size="sm" className="h-7 w-7 p-0">
                    <Wand2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              {/* Second Row: @ Menu, Selected Tags, Memories, Rules */}
              <div className="flex items-center gap-1 flex-wrap">
                {/* @ - Lists/Tasks Menu */}
                <Popover open={isListsMenuOpen} onOpenChange={setIsListsMenuOpen}>
                  <PopoverTrigger asChild>
                    <Button variant="ghost" size="sm" className="h-7 w-7 p-0">
                      <AtSign className="h-3 w-3" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-64 p-2" align="start">
                    <div className="space-y-2">
                      <div className="font-medium text-sm">Lists</div>
                      {mockLists.map((list) => (
                        <button
                          key={list.id}
                          type="button"
                          onClick={() => {
                            addTag({ id: list.id, type: 'list', name: list.name });
                            setIsListsMenuOpen(false);
                          }}
                          className="w-full text-left px-2 py-1 hover:bg-accent/20 rounded text-sm"
                        >
                          {list.name}
                        </button>
                      ))}
                    </div>
                  </PopoverContent>
                </Popover>

                {/* Brain - Memories */}
                <Popover open={isMemoriesOpen} onOpenChange={setIsMemoriesOpen}>
                  <PopoverTrigger asChild>
                    <Button variant="ghost" size="sm" className="h-7 w-7 p-0">
                      <Brain className="h-3 w-3" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-48 p-2" align="start">
                    <div className="text-sm">
                      <div className="font-medium mb-2">Memories</div>
                      <div className="text-muted-foreground text-xs">
                        Access your saved memories and context
                      </div>
                    </div>
                  </PopoverContent>
                </Popover>

                {/* Book - Rules & Guidelines */}
                <Popover open={isRulesOpen} onOpenChange={setIsRulesOpen}>
                  <PopoverTrigger asChild>
                    <Button variant="ghost" size="sm" className="h-7 w-7 p-0">
                      <BookOpen className="h-3 w-3" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-48 p-2" align="start">
                    <div className="text-sm">
                      <div className="font-medium mb-2">Rules & Guidelines</div>
                      <div className="text-muted-foreground text-xs">
                        Configure AI behavior and guidelines
                      </div>
                    </div>
                  </PopoverContent>
                </Popover>

                {/* Selected Tags Display - positioned after all icons */}
                {selectedTags.map((tag) => (
                  <div
                    key={`${tag.type}-${tag.id}`}
                    className="inline-flex items-center gap-1 px-2 py-1 bg-accent/20 text-accent-foreground rounded-md text-xs"
                  >
                    <span>{tag.name}</span>
                    <button
                      type="button"
                      onClick={() => removeTag(tag.id, tag.type)}
                      className="hover:bg-accent/30 rounded-sm p-0.5"
                      title={`Remove ${tag.name}`}
                      aria-label={`Remove ${tag.name}`}
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </MobileDialogContent>
    </MobileDialog>
  );
}
